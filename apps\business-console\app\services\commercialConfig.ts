import { ApiResponse } from "~/types/api/Api";
import { API_BASE_URL, apiRequest } from "~/utils/api";

export type SellerCommercialConfig = {
  id: number;

  // Seller Sales Comm fields
  sellerScPkg: number;
  sellerScPc: number;
  sellerScTax: number;

  // Seller Platform Comm fields  
  sellerPcPkg: number;
  sellerPcPc: number;
  sellerPcMm: number; // monthly minimum
  sellerPcMf: number; // monthly fixed
  sellerPcTax: number;

  // Supplier Sales Commission fields
  supplierScPkg: number;
  supplierScPc: number;
  supplierScTax: number;

  // Supplier Platform Commission fields
  supplierPcPkg: number;
  supplierPcPc: number;
  supplierPcTax: number;

  // Distributor Handling Charge fields
  distributorHcPkg: number;
  distributorHcPc: number;
  distributorHcTax: number;

  // Seller Handling Charge fields
  sellerHcSlab: number;
  sellerHcPo: number;
  sellerHcPc: number;
  sellerHcPkg: number;
  sellerHcTax: number;

  // Item fields
  itemTax: number;

  // Customer Platform Fee fields
  cpfPo: number;
  cpfPc: number;
  cpfTax: number;

  // Seller Delivery Charge tax
  sellerDcTax: number;
};

export async function getSellerCommercialConfig(
  sellerId: number,
  request: Request
): Promise<ApiResponse<SellerCommercialConfig>> {
  try {
    const response = await apiRequest<SellerCommercialConfig>(
      `${API_BASE_URL}/bc/mnetadmin/seller/${sellerId}/commconfig`,
      'GET',
      undefined,
      {},
      true,
      request
    )
    if (response) {
      return response;
    }
    
    throw new Error("No response received from commercial config API");
  }
  catch (err) {
    throw new Error("Failed to fetch commercial config");
  }
}

export async function updateSellerCommercialConfig(
  sellerId: number,
  commConfigId: number,
  data: Partial<SellerCommercialConfig>,
  request?: Request
  ): Promise<ApiResponse<SellerCommercialConfig>> {
  try {
    const response = await apiRequest<SellerCommercialConfig>(
      `${API_BASE_URL}/bc/mnetadmin/seller/${sellerId}/commconfig/${commConfigId}`,
      'PUT',
      data,
      {},
      true,
      request
    )
    if (response) {
      return response;
    }
    
    throw new Error("No response received from commercial config API");
  }
  catch (err) {
    throw new Error("Failed to fetch commercial config");
  }
}