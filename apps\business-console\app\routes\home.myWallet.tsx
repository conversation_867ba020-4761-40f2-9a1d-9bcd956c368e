import { LoaderFunction } from "@remix-run/node";
import { useLoaderD<PERSON>, useNavigate, useSearchParams } from "@remix-run/react";
import { useState } from "react";
import { Wallet, CalendarIcon, History, ArrowLeft } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Badge } from "~/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Popover, PopoverContent, PopoverTrigger, PopoverClose } from "~/components/ui/popover";
import { Calendar } from "~/components/ui/calendar";
import { cn } from "~/lib/utils";
import { format } from "date-fns";
import { DateRange } from "react-day-picker";
import dayjs from "dayjs";
import { WalletInfo, WalletHistory } from "~/types/api/businessConsoleService/Payouts";
import { getWalletInfo, getWalletHistory } from "~/services/payments";
import { withAuth, withResponse } from "~/utils/auth-utils";
import { formatCurrency } from "~/utils/format";
import ResponsivePagination from "~/components/ui/responsivePagination";
import SpinnerLoader from "~/components/loader/SpinnerLoader";
import { Alert, AlertDescription } from "~/components/ui/alert";

interface LoaderData {
  walletInfo: WalletInfo;
  walletHistory: {
    walletEntries: WalletHistory[];
    totalElements: number;
    totalPages: number;
    currentPage: number;
    pageSize: number;
    hasNext: boolean;
    hasPrevious: boolean;
  };
  pageNo: number;
  pageSize: number;
  fromDate?: string;
  toDate?: string;
  error?: string;
}

export const loader: LoaderFunction = withAuth(async ({ request }) => {
  const url = new URL(request.url);
  const pageNo = Number(url.searchParams.get("pageNo")) || 0;
  const pageSize = Number(url.searchParams.get("pageSize")) || 100;
  const fromDate = url.searchParams.get("fromDate") || undefined;
  const toDate = url.searchParams.get("toDate") || undefined;

  try {
    const [walletResponse, historyResponse] = await Promise.all([
      getWalletInfo(request),
      getWalletHistory(request, pageNo, pageSize, fromDate, toDate)
    ]);

    return withResponse({
      walletInfo: walletResponse.data,
      walletHistory: historyResponse.data,
      pageNo,
      pageSize,
      fromDate,
      toDate
    }, walletResponse.headers);
  } catch (error) {
    console.error("My Wallet loader error:", error);

    return withResponse({
      walletInfo: null,
      walletHistory: {
        walletEntries: [],
        totalElements: 0,
        totalPages: 0,
        currentPage: 0,
        pageSize: 10,
        hasNext: false,
        hasPrevious: false
      },
      pageNo,
      pageSize,
      fromDate,
      toDate,
      error: "Failed to load wallet data"
    }, new Headers());
  }
});

export default function MyWallet() {
  const { walletInfo, walletHistory, pageNo, pageSize, fromDate, toDate, error } = useLoaderData<LoaderData>();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  // Date filter state
  const [filterDate, setFilterDate] = useState<DateRange>({
    from: fromDate ? new Date(fromDate) : undefined,
    to: toDate ? new Date(toDate) : undefined,
  });
  const [dateRange, setDateRange] = useState<DateRange>({
    from: fromDate ? new Date(fromDate) : undefined,
    to: toDate ? new Date(toDate) : undefined,
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getTransactionTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case 'credit':
        return 'bg-green-100 text-green-800';
      case 'debit':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const handlePageChange = (newPage: number) => {
    const params = new URLSearchParams(searchParams);
    params.set("pageNo", newPage.toString());
    navigate(`?${params.toString()}`);
  };

  const handleDateFilter = () => {
    const params = new URLSearchParams(searchParams);

    if (dateRange.from) {
      const fromDateFormat = dayjs(dateRange.from).format('YYYY-MM-DD');
      params.set("fromDate", fromDateFormat);
    } else {
      params.delete("fromDate");
    }

    if (dateRange.to) {
      const toDateFormat = dayjs(dateRange.to).format('YYYY-MM-DD');
      params.set("toDate", toDateFormat);
    } else {
      params.delete("toDate");
    }

    setFilterDate(dateRange);
    params.set("pageNo", "0");
    navigate(`?${params.toString()}`);
  };

  const clearDateFilter = () => {
    const params = new URLSearchParams(searchParams);
    params.delete("fromDate");
    params.delete("toDate");
    params.set("pageNo", "0");
    setFilterDate({ from: undefined, to: undefined });
    setDateRange({ from: undefined, to: undefined });
    navigate(`?${params.toString()}`);
  };

  if (error) {
    return (
      <div className="p-6">
        <Alert className="border-red-200 bg-red-50">
          <AlertDescription className="text-red-800">
            {error}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!walletInfo) {
    return <SpinnerLoader loading={true} />;
  }

  return (
    <div className="container mx-auto p-4 sm:p-6 space-y-4 sm:space-y-6 max-w-7xl">

      {/* Page Header */}
      <div>
        <Button variant="secondary" size="sm" onClick={() => navigate(-1)}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Go Back
        </Button>
        <div className="mt-4">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 break-words">My Wallet</h1>
          <p className="text-sm sm:text-base text-gray-600 mt-1">Manage your wallet and view wallet history</p>
        </div>
      </div>

      {/* Wallet Information Card */}
      <Card className="border-0 shadow-md">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg p-3 sm:p-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg flex-shrink-0">
                <Wallet className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
              </div>
              <div className="min-w-0 flex-1">
                <CardTitle className="text-lg sm:text-xl text-gray-900 break-words">Wallet Balance</CardTitle>
                <CardDescription className="text-sm sm:text-base text-gray-600 break-words">
                  Available funds in your wallet
                </CardDescription>
              </div>
            </div>
            <div className="text-left sm:text-right">
              <div className="text-2xl sm:text-3xl ml-1 font-bold text-gray-900 break-all">
                {formatCurrency(walletInfo.walletBalance)}
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Wallet History Section */}
      <Card className="border-0 shadow-md">
        <CardHeader className="bg-gradient-to-r from-gray-50 to-slate-50 rounded-t-lg p-2 sm:p-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-gray-100 rounded-lg flex-shrink-0">
                <History className="h-5 w-5 sm:h-6 sm:w-6 text-gray-600" />
              </div>
              <div className="min-w-0 flex-1">
                <CardTitle className="text-lg sm:text-xl text-gray-900 break-words">Wallet History</CardTitle>
                <CardDescription className="text-sm sm:text-base text-gray-600 break-words">
                  View all your wallet transactions
                </CardDescription>
              </div>
            </div>

            {/* Date Filter */}
            <div className="flex flex-col sm:flex-row gap-2">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full sm:w-auto justify-start text-left font-normal",
                      !filterDate.from && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {filterDate?.from ? (
                      filterDate.to ? (
                        <>
                          {format(filterDate.from, "LLL dd, y")} - {format(filterDate.to, "LLL dd, y")}
                        </>
                      ) : (
                        format(filterDate.from, "LLL dd, y")
                      )
                    ) : (
                      <span>Pick a date range</span>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="end">
                  <Calendar
                    initialFocus
                    selected={dateRange}
                    mode="range"
                    onSelect={(range: DateRange | undefined) => {
                      if (!range?.from) return;
                      setDateRange({
                        from: range.from,
                        to: range.to || undefined,
                      });
                    }}
                  />
                  <div className="flex gap-2 p-3">
                    <PopoverClose className="flex-1">
                      <Button
                        variant="outline"
                        className="w-full"
                        onClick={() => handleDateFilter()}
                      >
                        Apply Filter
                      </Button>
                    </PopoverClose>
                    <Button
                      variant="ghost"
                      className="flex-1"
                      onClick={clearDateFilter}
                    >
                      Clear
                    </Button>
                  </div>
                </PopoverContent>
              </Popover>

              {(fromDate || toDate) && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearDateFilter}
                  className="text-gray-500 hover:text-gray-700"
                >
                  Clear Filter
                </Button>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-0">
          {walletHistory?.walletEntries?.length > 0 ? (
            <>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="bg-gray-50">
                      <TableHead className="font-semibold text-gray-900">Date & Time</TableHead>
                      <TableHead className="font-semibold text-gray-900 hidden sm:table-cell">Narration</TableHead>
                      <TableHead className="font-semibold text-gray-900 text-right">Credit</TableHead>
                      <TableHead className="font-semibold text-gray-900 text-right">Debit</TableHead>
                      <TableHead className="font-semibold text-gray-900 text-right">Balance</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {walletHistory?.walletEntries?.map((entry) => (
                      <TableRow key={entry.id} className="hover:bg-gray-50">
                        <TableCell className="font-medium whitespace-nowrap">
                          {formatDate(entry.transactionTime)}
                          <div className="mt-1 sm:hidden">{entry.narration || entry.note || '-'}</div>
                        </TableCell>
                        <TableCell className="hidden sm:table-cell" title={entry.narration}>
                          {entry.narration || entry.note || '-'}
                        </TableCell>
                        <TableCell className="text-right font-medium text-green-600 whitespace-nowrap">
                          {`₹ ${entry.creditValue}`}
                        </TableCell>
                        <TableCell className="text-right font-medium text-red-600 whitespace-nowrap">
                          {`₹ ${entry.debitValue}`}
                        </TableCell>
                        <TableCell className="text-right font-semibold whitespace-nowrap">
                          {`₹ ${entry.balance}`}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </>
          ) : (
            <div className="text-center py-12">
              <History className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No transactions found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {fromDate || toDate ? 'No transactions found for the selected date range.' : 'Your wallet history will appear here.'}
              </p>
            </div>
          )}

          {/* Pagination */}
          {walletHistory?.totalPages > 1 && (
            <div className="border-t bg-gray-50 px-4 py-3">
              <div>
                <ResponsivePagination
                  currentPage={pageNo}
                  totalPages={walletHistory.totalPages}
                  onPageChange={handlePageChange}
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}