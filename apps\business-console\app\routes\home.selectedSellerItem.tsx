import { useState, useEffect } from 'react';
import { Form, json, useActionData, useFetcher, useLoaderData, useNavigate, useSearchParams } from "@remix-run/react";
import { withAuth, withResponse } from "~/utils/auth-utils";
import { Pencil, Trash, Search, X } from "lucide-react";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "~/components/ui/tabs";
import { ResponsiveTable } from '~/components/ui/responsiveTable';
import { Button } from '~/components/ui/button';
import SpinnerLoader from '~/components/loader/SpinnerLoader';
import { AddOnGroup, SelectedItem } from '~/types/api/businessConsoleService/MyItemList';
import { createItemAddonGroup, createItemVariation, deleteItemAddonGroup, deleteItemVarition, getSellerAddonGroups, getSellerItemVariation } from '~/services/sellerItem.service';
import { MyAddonGroupData, MyVariationData } from '~/types/api/businessConsoleService/SellerManagement';
import { getAddonsGroups, getVariation } from '~/services/businessConsoleService';
import SelectedItemAddons from '~/components/common/SelectedItemAddons';
import SelectedItemVariation from '~/components/common/selectedItemVariation';
import { useDebounce } from '~/hooks/useDebounce';
import { getItemCommercialConfig, SellerCommercialConfig } from '~/services/commercialConfig';

interface LoaderData {
      seAddonGroupData?: AddOnGroup[];
      variationList: SelectedItem[];
      commercialConfig: SellerCommercialConfig;
      itemName: string;
      itemId: number;
      sellerId: number;
}

interface ActionData {
      selectedItemGruoupData: MyAddonGroupData[];
      sucessMessage: string;
      ErrorMessage: string;
      sucess: boolean;
}

interface VarActionData {
      itemVariation: MyVariationData[];
      sucess: boolean;
}

export const loader = withAuth(async ({ request, user }) => {
      const url = new URL(request.url);
      const sellerId = Number(url.searchParams.get("sellerId"));
      const activeTab = url.searchParams.get("activeTab") || 'Variations';
      const page = Number(url.searchParams.get("page") || "0");
      const pageSize = Number(url.searchParams.get("pageSize") || "50");
      const matchBy = url.searchParams.get("matchBy") || "";
      const itemId = Number(url.searchParams.get("itemId"));
      const itemName = url.searchParams.get("itemName");
      let response;
      let variationList: SelectedItem[] = [];
      let seAddonGroupData: AddOnGroup[] = [];
      let commercialConfig: Partial<SellerCommercialConfig> = {};

      try {
            switch (activeTab) {
                  case "Variations":
                        response = await getSellerItemVariation(sellerId, page, pageSize, matchBy, itemId, request);
                        variationList = response?.data || [];
                        break;
                  case "AddonsGroups":
                        response = await getSellerAddonGroups(sellerId, page, pageSize, matchBy, itemId, request);
                        seAddonGroupData = response?.data || [];
                        break;
                  case "CommercialConfig":
                        response = await getItemCommercialConfig(itemId, request);
                        commercialConfig = response?.data || {};
                        break;
            }
            return withResponse({ variationList, itemName, seAddonGroupData, commercialConfig, itemId, sellerId }, response?.headers);
      } catch (error) {
            console.error("Error in loader:", error);
            return json({ variationList: [], seAddonGroupData: [], commercialConfig: {} });
      }
});

export const action = withAuth(async ({ request }) => {
      const formData = await request.formData();
      const sellerId = Number(formData.get("sellerId"));
      const pageSize = Number(formData.get("pageSize") || 50);
      const page = Number(formData.get("page") || "0");
      const matchBy = formData.get("matchBy") as string;
      const actionType = formData.get("actionType") as string;

      if (actionType === "getItemVariations") {
            try {
                  const variationList = await getVariation(sellerId, page, pageSize, matchBy, request);
                  const itemVariation = variationList.data;
                  return withResponse({ itemVariation }, variationList.headers);
            } catch (error) {
                  console.error("Error loading variations:", error);
                  throw new Response('Failed to load variations', { status: 500 });
            }
      }

      if (actionType === "getAddonsGroupMap") {
            try {
                  const addonsgList = await getAddonsGroups(sellerId, page, pageSize, matchBy, request);
                  const selectedItemGruoupData = addonsgList.data;
                  return withResponse({ selectedItemGruoupData }, addonsgList.headers);
            } catch (error) {
                  console.error("Error loading addons groups:", error);
                  throw new Response('Failed to load addons', { status: 500 });
            }
      } else if (actionType === "actionItemVariation") {
            const itemId = Number(formData.get("itemId"));
            const sId = formData.get("sId") as string;
            const name = formData.get("name") as string;
            const groupName = formData.get("groupName");
            const price = Number(formData.get("price"));
            const strikeoffPrice = Number(formData.get("strikeoffPrice"));
            const seq = Number(formData.get("seq"));
            const id = Number(formData.get("selectedId"));
            const mode = formData.get("mode")

            const createPayload: any = {
                  sId,
                  name,
                  groupName,
                  seq,
                  price,
                  strikeoffPrice,
            };
            const editPayLoad: any = {
                  id,
                  sId,
                  name,
                  groupName,
                  seq,
                  price,
                  strikeoffPrice,
            };

            const finalPayLoad = mode == "EditMode" ? editPayLoad : createPayload

            try {
                  const addonsList = await createItemVariation(sellerId, itemId, finalPayLoad, request);
                  const selectedAddonsData = addonsList.data;
                  return withResponse({ selectedAddonsData, sucess: addonsList.statusCode === 200 }, addonsList.headers);
            } catch (error) {
                  console.error("Error loading addons groups:", error);
                  throw new Response('Failed to load addons', { status: 500 });
            }
      } else if (actionType === "actionItemAddonGroup") {
            const itemId = Number(formData.get("itemId"));
            const sequence = Number(formData.get("sequence"));
            const sId = formData.get("sId") as string;
            const name = formData.get("name") as string;
            const id = Number(formData.get("selectedId"));
            const minSelect = Number(formData.get("minSelect"));
            const maxSelect = Number(formData.get("maxSelect"));
            const description = formData.get("description") as string;
            const varient = formData.get("varient") as unknown as boolean;
            const mode = formData.get("mode")

            const createPayload: any = {
                  sId,
                  minSelect,
                  maxSelect,
                  name,
                  seq: sequence,
                  description,
                  varient,
            };
            const editPayload: any = {
                  id,
                  sId,
                  minSelect,
                  maxSelect,
                  name,
                  seq: sequence,
                  description,
                  varient,
            };


            const finalPayload = mode === "EditMode" ? editPayload : createPayload;

            try {
                  const addonsList = await createItemAddonGroup(sellerId, itemId, finalPayload, request);
                  const selectedAddonsData = addonsList.data;
                  return withResponse({ selectedAddonsData, success: true }, addonsList.headers);
            } catch (error) {
                  console.error("Error loading addons groups:", error);
                  throw new Response('Failed to load addons', { status: 500 });
            }
      } else if (actionType === "addonGroupDelete") {
            const ItemaddongId = Number(formData.get("ItemaddongId"));
            const itemId = Number(formData.get("itemId"));

            try {
                  const addonsList = await deleteItemAddonGroup(sellerId, ItemaddongId, itemId, request);
                  const selectedAddonsData = addonsList.data;
                  return withResponse({ selectedAddonsData }, addonsList.headers);
            } catch (error) {
                  console.error("Error loading addons groups:", error);
                  throw new Response('Failed to load addons', { status: 500 });
            }
      } else if (actionType === "itemVarDelete") {
            const varId = Number(formData.get("itemVarId"));
            const itemId = Number(formData.get("itemId"));

            try {
                  const response = await deleteItemVarition(sellerId, varId, itemId, request);
                  const itemVariation = response.data;
                  return withResponse({ itemVariation }, response.headers);
            } catch (error) {
                  console.error("Error deleting variation:", error);
                  throw new Response('Failed to delete variation', { status: 500 });
            }
      }
});

export default function SelectedSellerItem() {
      const { variationList, itemName, seAddonGroupData, commercialConfig, itemId, sellerId } = useLoaderData<LoaderData>();
      const [searchParams] = useSearchParams();
      const activeTab = searchParams.get("activeTab") || "Variations";
      const fetcher = useFetcher();
      const navigate = useNavigate();
      const loading = fetcher.state !== "idle";

      const [variationSearchTerm, setVariationSearchTerm] = useState('');
      const [addonGroupSearchTerm, setAddonGroupSearchTerm] = useState('');

      const addonGroupDebounce = useDebounce(addonGroupSearchTerm, 300)
      const variationDebounce = useDebounce(variationSearchTerm, 300)

      const [filteredVariations, setFilteredVariations] = useState<SelectedItem[]>(variationList);
      const [filteredAddonGroups, setFilteredAddonGroups] = useState<AddOnGroup[]>(seAddonGroupData || []);

      const variationHeaders = ["ID", "Name", "Group Name", "Price", "Strike Off Price", "Quantity", "", ""];
      const addonGroupHeaders = ["ID", "Name", "Description", "Min Select", "Max Select", "", "", ""];

      useEffect(() => {
            if (variationDebounce.length >= 3 && variationDebounce !== "") {
                  const filtered = (variationList || []).filter((item) =>
                        item.name?.toLowerCase().includes(variationDebounce.toLowerCase()) ||
                        item.groupName?.toLowerCase().includes(variationDebounce.toLowerCase())
                  );
                  console.log('Filtered addon groups:', filtered);
                  setFilteredVariations(filtered);
            }
            else {
                  setFilteredVariations(variationList || []);
            }

      }, [variationList, variationDebounce]);

      // Filter Addon Groups
      useEffect(() => {
            if (addonGroupDebounce.length >= 3 && addonGroupDebounce !== "") {
                  const filtered = (seAddonGroupData || []).filter((item) =>
                        item.name?.toLowerCase().includes(addonGroupDebounce.toLowerCase()) ||
                        item.description?.toLowerCase().includes(addonGroupDebounce.toLowerCase())
                  );
                  console.log('Filtered addon groups:', filtered);
                  setFilteredAddonGroups(filtered);
            }
            else {
                  setFilteredAddonGroups(seAddonGroupData || []);
            }

      }, [seAddonGroupData, addonGroupDebounce]);

      // Reset search term when switching tabs
      useEffect(() => {
            setVariationSearchTerm('');
            setAddonGroupSearchTerm('');
      }, [activeTab]);

      const handleTabChange = (newTab: string) => {
            navigate(`?itemId=${itemId}&itemName=${encodeURIComponent(itemName)}&activeTab=${newTab}&sellerId=${sellerId}`);
      };

      const [itemselectedGroupAddons, setItemselectedGroupAddons] = useState(false);
      const [selectedAddonsgData, setSelectedAddonsgData] = useState<MyAddonGroupData[]>();
      const [selectedItemGdata, setSelectedItemGdata] = useState<AddOnGroup>();
      const [selectedItemVarData, setSelectedtemVarData] = useState<SelectedItem>();
      const [selectedVariations, setSelectedVariations] = useState<MyVariationData[]>();
      const [itemSelectedVariation, setItemSelectedVariation] = useState(false);

      const addonitemgfetcher = useFetcher<ActionData>();
      const itemVaritonfetcher = useFetcher<VarActionData>();

      const actionData = useActionData<ActionData>();
      const varActionData = useActionData<VarActionData>();

      const [isEditopen, setIseditOpen] = useState(false);
      const [isVarEditOpen, setIsVarEditOpen] = useState(false);

      const handleDelete = (addonsData: AddOnGroup) => {
            const formData = new FormData();
            formData.append("actionType", "addonGroupDelete");
            formData.append("itemId", itemId.toString());
            formData.append("ItemaddongId", addonsData?.id?.toString() ?? "");
            formData.append("sellerId", sellerId.toString());
            addonitemgfetcher.submit(formData, { method: 'post' });
      };

      const handleItemVariationsDelete = (variationData: SelectedItem) => {
            const formData = new FormData();
            formData.append("actionType", "itemVarDelete");
            formData.append("itemId", itemId.toString());
            formData.append("itemVarId", variationData?.id?.toString() ?? "");
            formData.append("sellerId", sellerId.toString());
            itemVaritonfetcher.submit(formData, { method: 'post' });
      };

      useEffect(() => {
            if (itemVaritonfetcher.state === "idle") {
                  if (varActionData?.itemVariation) {
                        setSelectedVariations(varActionData?.itemVariation);
                        setItemSelectedVariation(true);
                        setIsVarEditOpen(false)
                  } else {
                        setSelectedVariations([]);
                        setItemSelectedVariation(false);
                        setIsVarEditOpen(false)

                  }
            }
      }, [varActionData]);

      useEffect(() => {
            if (addonitemgfetcher.state === "idle") {
                  if (actionData?.selectedItemGruoupData) {
                        setSelectedAddonsgData(actionData.selectedItemGruoupData);
                        setItemselectedGroupAddons(true);
                        setIseditOpen(false);
                  } else {
                        setSelectedAddonsgData([]);
                        setIseditOpen(false);
                        setItemselectedGroupAddons(false);
                  }
            }
      }, [actionData]);

      const handleSelectedItemGroupData = (row: AddOnGroup) => {
            setItemselectedGroupAddons(true);
            setSelectedItemGdata(row);
            setIseditOpen(true);
      };

      const handleSelectedItemVarData = (row: SelectedItem) => {
            setItemSelectedVariation(true);

            setSelectedtemVarData(row);
            setIsVarEditOpen(true);
      };

      return (
            <div key={activeTab}>
                  {loading && <SpinnerLoader loading={loading} />}
                  <h1
                        className="mb-6 text-2xl font-bold text-gray-900 cursor-pointer hover:text-blue-600 transition-colors"
                        onClick={() => navigate(-1)}
                  >
                        Seller Item / {itemName}
                  </h1>

                  <Tabs value={activeTab} onValueChange={handleTabChange} className="bg-white rounded-lg shadow-sm border border-gray-200">
                        <TabsList className="border-b border-gray-200">
                              <TabsTrigger
                                    value="Variations"
                                    className="px-4 py-2 text-sm font-medium text-gray-700 data-[state=active]:text-blue-600 data-[state=active]:border-b-2 data-[state=active]:border-blue-600"
                              >
                                    Variations
                              </TabsTrigger>
                              <TabsTrigger
                                    value="AddonsGroups"
                                    className="px-4 py-2 text-sm font-medium text-gray-700 data-[state=active]:text-blue-600 data-[state=active]:border-b-2 data-[state=active]:border-blue-600"
                              >
                                    Addons Groups
                              </TabsTrigger>
                              <TabsTrigger
                                    value="CommercialConfig"
                                    className="px-4 py-2 text-sm font-medium text-gray-700 data-[state=active]:text-blue-600 data-[state=active]:border-b-2 data-[state=active]:border-blue-600"
                              >
                                    Commercial Config
                              </TabsTrigger>
                        </TabsList>

                        <TabsContent value="Variations" className="p-4">
                              <div className="mb-4 relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                                    <input
                                          type="text"
                                          placeholder="Search variations by name or group name..."
                                          value={variationSearchTerm}
                                          onChange={(e) => setVariationSearchTerm(e.target.value)}
                                          className="w-full pl-10 pr-10 py-2 rounded-lg border border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 text-sm"
                                    />
                                    {variationSearchTerm && (
                                          <X
                                                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5 cursor-pointer hover:text-gray-600"
                                                onClick={() => setVariationSearchTerm('')}
                                          />
                                    )}
                              </div>
                              <ResponsiveTable
                                    headers={variationHeaders}
                                    data={filteredVariations}
                                    renderRow={(row: SelectedItem) => (
                                          <tr key={row.id} className="border-b hover:bg-gray-50 transition-colors">
                                                <td className="py-3 px-4 text-center text-sm text-gray-700">{row?.id}</td>
                                                <td className="py-3 px-4 text-center text-sm text-blue-600 cursor-pointer hover:underline" onClick={() => navigate(`/home/<USER>/td>
                                                <td className="py-3 px-4 text-center text-sm text-gray-700">{row?.groupName || "-"}</td>
                                                <td className="py-3 px-4 text-center text-sm text-gray-700">
                                                      {row?.price > 0 ? `₹ ${row?.price.toFixed(2)}` : '-'}
                                                </td>
                                                <td className="py-3 px-4 text-center text-sm text-gray-700">
                                                      {row?.strikeoffPrice > 0 ? `₹ ${row?.strikeoffPrice.toFixed(2)}` : '-'}
                                                </td>
                                                <td className="py-3 px-4 text-center text-sm text-gray-700">
                                                      {row?.qty > 0 ? `${row?.qty.toFixed(2)}` : '-'}
                                                </td>
                                                <td className="py-3 px-4 text-center">
                                                      <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            className="text-red-500 hover:text-red-700"
                                                            onClick={() => {
                                                                  if (confirm("Are you sure you want to delete this Item Variation?")) {
                                                                        handleItemVariationsDelete(row);
                                                                  }
                                                            }}
                                                      >
                                                            <Trash size={18} />
                                                      </Button>
                                                </td>
                                                <td className="py-3 px-4 text-center">
                                                      <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            className="text-blue-500 hover:text-blue-700"
                                                            onClick={() => handleSelectedItemVarData(row)}
                                                      >
                                                            <Pencil size={18} />
                                                      </Button>
                                                </td>
                                          </tr>
                                    )}
                              />
                              <Form method="post">
                                    <input name="sellerId" value={sellerId} hidden />
                                    <input name="matchBy" value={""} hidden />
                                    <input name="actionType" value={"getItemVariations"} hidden />
                                    <input name="pageSize" value={"50"} hidden />
                                    <Button
                                          className="fixed bottom-5 right-5 rounded-full bg-blue-600 text-white hover:bg-blue-700 shadow-lg transition-all duration-200 px-6 py-3"
                                          type="submit"
                                    >
                                          + Add Variation
                                    </Button>
                              </Form>
                              <SelectedItemVariation
                                    isOpen={itemSelectedVariation}
                                    items={selectedVariations || []}
                                    onClose={() => setItemSelectedVariation(false)}
                                    header={isVarEditOpen ? 'Edit Item Variation' : "Add Item Variation"}
                                    sellerId={sellerId}
                                    groupId={itemId}
                                    groupData={isVarEditOpen ? selectedItemVarData : undefined}
                                    isEdit={isVarEditOpen}
                              />
                        </TabsContent>

                        <TabsContent value="AddonsGroups" className="p-4">
                              <div className="mb-4 relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                                    <input
                                          type="text"
                                          placeholder="Search addon groups by name or description..."
                                          value={addonGroupSearchTerm}
                                          onChange={(e) => setAddonGroupSearchTerm(e.target.value)}
                                          className="w-full pl-10 pr-10 py-2 rounded-lg border border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 outline-none transition-all duration-200 text-sm"
                                    />
                                    {addonGroupSearchTerm && (
                                          <X
                                                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5 cursor-pointer hover:text-gray-600"
                                                onClick={() => setAddonGroupSearchTerm('')}
                                          />
                                    )}
                              </div>
                              <ResponsiveTable
                                    headers={addonGroupHeaders}
                                    data={filteredAddonGroups}

                                    renderRow={(row: AddOnGroup) => (
                                          <tr key={row.id} className="border-b hover:bg-gray-50 transition-colors">
                                                <td className="py-3 px-4 text-center text-sm text-gray-700">{row.id}</td>
                                                <td className="py-3 px-4 text-center text-sm text-blue-600 cursor-pointer hover:underline">{row.name}</td>
                                                <td className="py-3 px-4 text-center text-sm text-gray-700">{row.description || '-'}</td>
                                                <td className="py-3 px-4 text-center text-sm text-gray-700">{row.minSelect}</td>
                                                <td className="py-3 px-4 text-center text-sm text-gray-700">{row.maxSelect}</td>
                                                {/* <td className="py-3 px-4 text-center text-sm">
                                                      {row?.varient ? (
                                                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                                  <svg
                                                                        className="w-4 h-4 mr-1 text-green-500"
                                                                        fill="currentColor"
                                                                        viewBox="0 0 20 20"
                                                                        xmlns="http://www.w3.org/2000/svg"
                                                                  >
                                                                        <path
                                                                              fillRule="evenodd"
                                                                              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414  Foram1.414l2 2a1 1 0 001.414 0l4-4z"
                                                                              clipRule="evenodd" />
                                                                  </svg>
                                                                  Active
                                                            </span>
                                                      ) : (
                                                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                                  <svg
                                                                        className="w-4 h-4 mr-1 text-red-500"
                                                                        fill="currentColor"
                                                                        viewBox="0 0 20 20"
                                                                        xmlns="http://www.w3.org/2000/svg"
                                                                  >
                                                                        <path
                                                                              fillRule="evenodd"
                                                                              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 001.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                                                              clipRule="evenodd" />
                                                                  </svg>
                                                                  Inactive
                                                            </span>
                                                      )}
                                                </td> */}
                                                <td className="py-3 px-4 text-center">
                                                      <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            className="text-red-500 hover:text-red-700"
                                                            onClick={() => {
                                                                  if (confirm("Are you sure you want to delete this Item AddonGroup?")) {
                                                                        handleDelete(row);
                                                                  }
                                                            }}
                                                      >
                                                            <Trash size={18} />
                                                      </Button>
                                                </td>
                                                <td className="py-3 px-4 text-center">
                                                      <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            className="text-blue-500 hover:text-blue-700"
                                                            onClick={() => handleSelectedItemGroupData(row)}
                                                      >
                                                            <Pencil size={18} />
                                                      </Button>
                                                </td>
                                          </tr>
                                    )} />
                              <Form method="post">
                                    <input name="sellerId" value={sellerId} hidden />
                                    <input name="matchBy" value={""} hidden />
                                    <input name="actionType" value={"getAddonsGroupMap"} hidden />
                                    <input name="pageSize" value={"50"} hidden />
                                    <Button
                                          className="fixed bottom-5 right-5 rounded-full bg-blue-600 text-white hover:bg-blue-700 shadow-lg transition-all duration-200 px-6 py-3"
                                          type="submit"
                                    >
                                          + Add Addon Group
                                    </Button>
                              </Form>
                              <SelectedItemAddons
                                    isOpen={itemselectedGroupAddons}
                                    items={selectedAddonsgData || []}
                                    onClose={() => setItemselectedGroupAddons(false)}
                                    header={isEditopen ? `Edit Addon for ${itemName?.slice(0, 15)}` : `Create Addon for ${itemName?.slice(0, 15)}`}
                                    groupData={isEditopen ? selectedItemGdata : undefined}
                                    sellerId={sellerId}
                                    groupId={itemId}
                                    isEdit={isEditopen}

                              />
                        </TabsContent>
                        <TabsContent value="CommercialConfig" className="p-4">
                              commercial config
                        </TabsContent>
                  </Tabs>
            </div>
      );
}